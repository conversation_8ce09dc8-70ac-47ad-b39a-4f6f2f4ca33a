<?php

namespace App\Observers;

use App\Mail\ContactFormSubmitted;
use App\Mail\ContactFormThankYou;
use App\Models\ContactSubmission;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ContactSubmissionObserver
{
    /**
     * Handle the ContactSubmission "created" event.
     */
    public function created(ContactSubmission $contactSubmission): void
    {
        try {
            // Send notification email to business/admin
            $this->sendOwnerNotification($contactSubmission);
            
            // Send thank-you email to customer
            $this->sendCustomerThankYou($contactSubmission);
            
            Log::info('Contact form emails queued successfully', [
                'submission_id' => $contactSubmission->id,
                'customer_email' => $contactSubmission->email,
                'subject' => $contactSubmission->subject,
            ]);
            
        } catch (\Exception $e) {
            // Log the error but don't fail the contact submission
            Log::error('Failed to queue contact form emails', [
                'submission_id' => $contactSubmission->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Send notification email to business owner/admin
     */
    private function sendOwnerNotification(ContactSubmission $contactSubmission): void
    {
        try {
            // Load owner email from environment
            $ownerEmail = env('MAIL_OWNER_EMAIL', env('MAIL_FROM_ADDRESS', '<EMAIL>'));

            // Additional recipients can be configured via environment
            $additionalEmails = env('MAIL_CONTACT_FORM_RECIPIENTS');
            $additionalRecipients = $additionalEmails ? explode(',', $additionalEmails) : [];

            // Clean up additional recipients (trim whitespace)
            $additionalRecipients = array_map('trim', $additionalRecipients);
            $additionalRecipients = array_filter($additionalRecipients); // Remove empty values

            // Send to primary owner email
            Mail::to($ownerEmail)->queue(new ContactFormSubmitted($contactSubmission));

            // Send to additional recipients if configured
            if (!empty($additionalRecipients)) {
                foreach ($additionalRecipients as $recipient) {
                    if (filter_var($recipient, FILTER_VALIDATE_EMAIL)) {
                        Mail::to($recipient)->queue(new ContactFormSubmitted($contactSubmission));
                    }
                }
            }

            Log::info('Owner notification email queued', [
                'submission_id' => $contactSubmission->id,
                'owner_email' => $ownerEmail,
                'additional_recipients' => $additionalRecipients,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to queue owner notification email', [
                'submission_id' => $contactSubmission->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Send thank-you email to customer
     */
    private function sendCustomerThankYou(ContactSubmission $contactSubmission): void
    {
        try {
            Mail::to($contactSubmission->email, $contactSubmission->name)
                ->queue(new ContactFormThankYou($contactSubmission));
            
            Log::info('Customer thank-you email queued', [
                'submission_id' => $contactSubmission->id,
                'customer_email' => $contactSubmission->email,
                'customer_name' => $contactSubmission->name,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to queue customer thank-you email', [
                'submission_id' => $contactSubmission->id,
                'customer_email' => $contactSubmission->email,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Handle the ContactSubmission "updated" event.
     */
    public function updated(ContactSubmission $contactSubmission): void
    {
        // Log status changes for audit purposes
        if ($contactSubmission->isDirty('status')) {
            Log::info('Contact submission status changed', [
                'submission_id' => $contactSubmission->id,
                'old_status' => $contactSubmission->getOriginal('status'),
                'new_status' => $contactSubmission->status,
                'customer_email' => $contactSubmission->email,
            ]);
        }
    }

    /**
     * Handle the ContactSubmission "deleted" event.
     */
    public function deleted(ContactSubmission $contactSubmission): void
    {
        Log::warning('Contact submission deleted', [
            'submission_id' => $contactSubmission->id,
            'customer_email' => $contactSubmission->email,
            'subject' => $contactSubmission->subject,
        ]);
    }
}
